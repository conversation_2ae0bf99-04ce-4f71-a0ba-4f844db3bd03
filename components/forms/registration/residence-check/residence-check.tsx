"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { useSearchParams } from "next/navigation"
import { useRegisterForeignUser } from "@/utils/queries/foreign-registration"
import { toast } from "@/hooks/use-toast"

interface ResidenceCheckProps {
    onResidenceConfirmed: (isResident: boolean) => void
}

export function ResidenceCheck({ onResidenceConfirmed }: ResidenceCheckProps) {
    const [isLoading, setIsLoading] = useState(false)
    const [showConfirmDialog, setShowConfirmDialog] = useState(false)
    
    const searchParams = useSearchParams()
    const uuid = searchParams.get('uuid') || searchParams.get('identifier') || ''
    const foreignRegistrationMutation = useRegisterForeignUser()

    const handleResidentAnswer = async (isResident: boolean) => {
        if (isResident) {
            // Se é residente, procede normalmente
            setIsLoading(true)
            await new Promise(resolve => setTimeout(resolve, 300))
            onResidenceConfirmed(true)
            setIsLoading(false)
        } else {
            // Se não é residente, abre popup de confirmação
            setShowConfirmDialog(true)
        }
    }

    const handleConfirmForeignRegistration = () => {
        setShowConfirmDialog(false)
        
        if (!uuid) {
            toast({
                title: "❌ Erro",
                description: "UUID não encontrado.",
                variant: "destructive"
            })
            return
        }

        foreignRegistrationMutation.mutate(uuid, {
            onSuccess: (data) => {
                toast({
                    title: "✅ Registo confirmado!",
                    description: "O seu registo como cidadão no estrangeiro foi confirmado.",
                    variant: "default"
                })
                // Após sucesso, confirma que não é residente
                onResidenceConfirmed(false)
            },
            onError: (error: any) => {
                const errorMessage = error?.response?.data?.message ||
                                   error?.message ||
                                   "Erro ao registar como cidadão no estrangeiro."
                
                toast({
                    title: "❌ Erro no registo",
                    description: errorMessage,
                    variant: "destructive"
                })
            }
        })
    }

    const handleCancelConfirmation = () => {
        setShowConfirmDialog(false)
    }

    return (
        <>
            <div className="max-w-2xl mx-auto">
                <Card className="border-2 shadow-lg">
                    <CardHeader className="text-center pb-6">
                        <CardTitle className="text-2xl font-bold text-primary mb-2">
                            Verificação de Residência
                        </CardTitle>
                        <CardDescription className="text-lg">
                            Para prosseguir com o registo, precisamos de confirmar a sua residência.
                        </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        <div className="text-center">
                            <h3 className="text-xl font-semibold mb-4 text-gray-800">
                                É residente em Portugal?
                            </h3>
                            <p className="text-gray-600 mb-6">
                                Esta informação é necessária para determinar o processo de registo adequado.
                            </p>
                        </div>
                        
                        <div className="flex flex-col sm:flex-row gap-4 justify-center">
                            <Button
                                onClick={() => handleResidentAnswer(true)}
                                disabled={isLoading || foreignRegistrationMutation.isPending}
                                size="lg"
                                className="bg-primary hover:bg-primary/90 text-white font-semibold py-3 px-8 text-lg"
                            >
                                {isLoading ? "A processar..." : "Sim, sou residente em Portugal"}
                            </Button>
                            
                            <Button
                                onClick={() => handleResidentAnswer(false)}
                                disabled={isLoading || foreignRegistrationMutation.isPending}
                                variant="outline"
                                size="lg"
                                className="border-2 border-gray-300 hover:bg-gray-50 font-semibold py-3 px-8 text-lg"
                            >
                                {foreignRegistrationMutation.isPending ? "A processar..." : "Não, não sou residente"}
                            </Button>
                        </div>
                        
                        <div className="text-center text-sm text-gray-500 mt-6">
                            <p>
                                ℹ️ Esta informação é utilizada apenas para fins de registo e não será partilhada.
                            </p>
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Dialog de Confirmação */}
            <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
                <DialogContent className="sm:max-w-md bg-white">
                    <DialogHeader>
                        <DialogTitle className="text-center text-xl font-bold text-primary">
                            🌍 Confirmar Registo no Estrangeiro
                        </DialogTitle>
                        <DialogDescription className="text-center text-gray-600 mt-3">
                            Confirma que pretende registar o seu UUID como cidadão português no estrangeiro?
                        </DialogDescription>
                    </DialogHeader>

                    <DialogFooter className="flex flex-col sm:flex-row gap-2 sm:gap-2 mt-6">
                        <Button 
                            variant="outline" 
                            onClick={handleCancelConfirmation}
                            disabled={foreignRegistrationMutation.isPending}
                            className="w-full sm:w-auto order-2 sm:order-1"
                        >
                            Cancelar
                        </Button>
                        <Button 
                            onClick={handleConfirmForeignRegistration}
                            disabled={foreignRegistrationMutation.isPending}
                            className="w-full sm:w-auto order-1 sm:order-2 bg-primary hover:bg-primary/90"
                        >
                            {foreignRegistrationMutation.isPending ? "A registar..." : "Confirmar Registo"}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </>
    )
}
