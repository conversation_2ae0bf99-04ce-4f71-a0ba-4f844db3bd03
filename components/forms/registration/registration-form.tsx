"use client"

import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { useEffect, useState } from "react"
import { useSearchParams } from "next/navigation"
import { toast } from "@/hooks/use-toast"
import { formSchema, type FormValues } from "./schema"
import { useRegisterUser, useValidateRegistration } from "@/utils/queries/register"
import { useRegisterForeignUser } from "@/utils/queries/foreign-registration"
import { RegisterCredentials } from "@/utils/types/user"
import { LoadingCard, RegistrationFormCard } from "../fields"
import { ResidenceCheck, NonResidentMessage, InvalidUuidMessage, ConflictErrorMessage } from "./residence-check"

export default function RegistrationForm() {
    const searchParams = useSearchParams()
    const uuid = searchParams.get('uuid') || searchParams.get('identifier') || ''
    
    // Estados para verificação de residência
    const [residenceChecked, setResidenceChecked] = useState(false)
    const [isPortugalResident, setIsPortugalResident] = useState<boolean | null>(null)
    
    const validation = useValidateRegistration(uuid)
    const registerMutation = useRegisterUser()
    const foreignRegistrationMutation = useRegisterForeignUser()
    
    const form = useForm<FormValues>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            full_name: "",
            email: "",
            nif: "",
            phone_number: "",
            birthdate: "",
            gender: "",
            distrito: "",
            concelho: "",
            freguesia: "",
            country_code: "+351",
            country: "Portugal",
            address: "",
            postal_code: "",
            locality: "",
        },
    })

    // Set email from API validation (mantido o useEffect para definir email)
    useEffect(() => {
        if (validation.isSuccess && validation.data?.data?.email) {
            form.setValue('email', validation.data.data.email, { 
                shouldDirty: true, 
                shouldTouch: true 
            })
        }
    }, [validation.isSuccess, validation.data, form])

    const mapFormDataToAPI = (formData: FormValues): RegisterCredentials => ({
        full_name: formData.full_name || '',
        registration_id: uuid || '',
        nif: formData.nif || '',
        email: formData.email || '',
        phone_number: formData.phone_number || '',
        birthdate: formData.birthdate || '',
        gender: formData.gender || '',
        district: formData.distrito || '',
        municipality: formData.concelho || '',
        parish: formData.freguesia || '',
        country_code: formData.country_code || '+351',
        country: formData.country || 'Portugal',
        address: formData.address || '',
        postal_code: formData.postal_code || '',
        locality: formData.locality || '',
    })

    const onSubmit = (values: FormValues) => {
        const apiData = mapFormDataToAPI(values)
        
        registerMutation.mutate(apiData, {
            onSuccess: () => {
                toast({
                    title: "✅ Registo enviado com sucesso!",
                    description: "Os seus dados foram registados com sucesso. Será redirecionado para o login...",
                    variant: "default"
                })
                form.reset()
                setTimeout(() => {
                    window.location.href = 'https://portalchega.pt/login/'
                }, 2000)
            },
            onError: (error: any) => {
                const errorMessage = error?.response?.data?.message ||
                                   error?.message ||
                                   "Ocorreu um erro no registo. Tente novamente."
                
                toast({
                    title: "❌ Erro no registo",
                    description: errorMessage,
                    variant: "destructive"
                })
            }
        })
    }

    const handleResidenceConfirmed = (isResident: boolean) => {
        setIsPortugalResident(isResident)
        setResidenceChecked(true)
    }

    const handleGoBackToResidenceCheck = () => {
        setResidenceChecked(false)
        setIsPortugalResident(null)
    }

    if (validation.isLoading) {
        return <LoadingCard />
    }

    if (validation.isConflictError) {
        return <ConflictErrorMessage />
    }

    if (!uuid || (!validation.isSuccess && !validation.isConflictError)) {
        return <InvalidUuidMessage />
    }

    if (!residenceChecked) {
        return <ResidenceCheck onResidenceConfirmed={handleResidenceConfirmed} />
    }

    if (isPortugalResident === false) {
        return <NonResidentMessage onGoBack={handleGoBackToResidenceCheck} />
    }

    if (!validation.isUserRegistered && !validation.isConflictError) {
        return (
            <RegistrationFormCard
                form={form}
                onSubmit={onSubmit}
                isSubmitting={registerMutation.isPending}
                isDisabled={registerMutation.isPending || validation.isLoading || uuid === ''}
            />
        )
    }
    
    return null
}
