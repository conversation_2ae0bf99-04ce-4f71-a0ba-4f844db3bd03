"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useValidateRegistration } from "@/utils/queries/register"

export default function DebugValidation() {
    const [identifier, setIdentifier] = useState("")
    const [foreign, setForeign] = useState(false)
    const [shouldValidate, setShouldValidate] = useState(false)

    const validation = useValidateRegistration(
        shouldValidate ? identifier : "", 
        foreign
    )

    const handleValidate = () => {
        if (identifier.trim()) {
            setShouldValidate(true)
        }
    }

    const handleReset = () => {
        setIdentifier("")
        setForeign(false)
        setShouldValidate(false)
    }

    return (
        <div className="container mx-auto py-10 px-4">
            <Card className="max-w-2xl mx-auto">
                <CardHeader>
                    <CardTitle>Debug - Validação de Registo</CardTitle>
                    <CardDescription>
                        Ferramenta para testar a validação de registos
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                    <div className="space-y-4">
                        <div>
                            <Label htmlFor="identifier">UUID/Identifier</Label>
                            <Input
                                id="identifier"
                                value={identifier}
                                onChange={(e) => setIdentifier(e.target.value)}
                                placeholder="Insira o UUID ou identifier"
                            />
                        </div>
                        
                        <div className="flex items-center space-x-2">
                            <input
                                type="checkbox"
                                id="foreign"
                                checked={foreign}
                                onChange={(e) => setForeign(e.target.checked)}
                                className="rounded"
                            />
                            <Label htmlFor="foreign">Registo estrangeiro</Label>
                        </div>

                        <div className="flex space-x-2">
                            <Button onClick={handleValidate} disabled={!identifier.trim()}>
                                Validar
                            </Button>
                            <Button variant="outline" onClick={handleReset}>
                                Reset
                            </Button>
                        </div>
                    </div>

                    {shouldValidate && (
                        <div className="space-y-4">
                            <h3 className="text-lg font-semibold">Resultado:</h3>
                            
                            <div className="bg-gray-50 p-4 rounded-lg">
                                <div className="space-y-2">
                                    <p><strong>Status:</strong> {validation.isLoading ? "Carregando..." : validation.isSuccess ? "Sucesso" : validation.isError ? "Erro" : "Aguardando"}</p>
                                    
                                    {validation.isSuccess && (
                                        <>
                                            <p><strong>Email:</strong> {validation.email || "N/A"}</p>
                                            <p><strong>Usuário Registrado:</strong> {validation.isUserRegistered ? "Sim" : "Não"}</p>
                                            <p><strong>Usuário Estrangeiro:</strong> {validation.isForeignUser ? "Sim" : "Não"}</p>
                                            <p><strong>Mensagem:</strong> {validation.validationMessage || "N/A"}</p>
                                            <p><strong>Erro de Conflito:</strong> {validation.isConflictError ? "Sim" : "Não"}</p>
                                        </>
                                    )}
                                    
                                    {validation.isError && (
                                        <p><strong>Erro:</strong> {(validation.error as any)?.message || "Erro desconhecido"}</p>
                                    )}
                                </div>
                            </div>

                            {validation.data && (
                                <div className="bg-blue-50 p-4 rounded-lg">
                                    <h4 className="font-semibold mb-2">Dados Completos:</h4>
                                    <pre className="text-sm overflow-auto">
                                        {JSON.stringify(validation.data, null, 2)}
                                    </pre>
                                </div>
                            )}
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    )
}
