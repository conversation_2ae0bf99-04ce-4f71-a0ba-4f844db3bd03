"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import parsePhoneNumber from 'libphonenumber-js'
import { country_code } from "@/components/forms/fields/constants"

// Criar mapeamento de código de país para ISO
const countryCodeToISO: Record<string, string> = {}
country_code.forEach(item => {
    countryCodeToISO[item.code] = item.iso.toUpperCase()
})

// Funções de validação (copiadas do schema.ts)
function validatePhoneNumberPossible(phoneNumber: string, countryCode: string): boolean {
    if (!phoneNumber || !countryCode) return false;

    try {
        const isoCode = countryCodeToISO[countryCode];
        
        let parsedNumber;
        if (isoCode) {
            parsedNumber = parsePhoneNumber(phoneNumber, isoCode as any);
        } else {
            const fullNumber = countryCode + phoneNumber;
            parsedNumber = parsePhoneNumber(fullNumber);
        }

        if (parsedNumber) {
            return parsedNumber.isPossible();
        }

        return false;
    } catch {
        return false;
    }
}

function validatePhoneNumberValid(phoneNumber: string, countryCode: string): boolean {
    if (!phoneNumber || !countryCode) return false;
    
    try {
        const isoCode = countryCodeToISO[countryCode];
        
        let parsedNumber;
        if (isoCode) {
            parsedNumber = parsePhoneNumber(phoneNumber, isoCode as any);
        } else {
            const fullNumber = countryCode + phoneNumber;
            parsedNumber = parsePhoneNumber(fullNumber);
        }

        if (parsedNumber) {
            return parsedNumber.isValid();
        }

        return false;
    } catch {
        return false;
    }
}

export default function PhoneValidationTest() {
    const [phoneNumber, setPhoneNumber] = useState("")
    const [countryCode, setCountryCode] = useState("+351")
    const [results, setResults] = useState<any>(null)

    const handleTest = () => {
        try {
            const isoCode = countryCodeToISO[countryCode];
            
            let parsedNumber;
            if (isoCode) {
                parsedNumber = parsePhoneNumber(phoneNumber, isoCode as any);
            } else {
                const fullNumber = countryCode + phoneNumber;
                parsedNumber = parsePhoneNumber(fullNumber);
            }

            const isPossible = validatePhoneNumberPossible(phoneNumber, countryCode);
            const isValid = validatePhoneNumberValid(phoneNumber, countryCode);

            setResults({
                isoCode,
                parsedNumber: parsedNumber ? {
                    number: parsedNumber.number,
                    nationalNumber: parsedNumber.nationalNumber,
                    countryCallingCode: parsedNumber.countryCallingCode,
                    country: parsedNumber.country,
                    isPossible: parsedNumber.isPossible(),
                    isValid: parsedNumber.isValid(),
                } : null,
                isPossible,
                isValid,
                error: null
            });
        } catch (error: any) {
            setResults({
                error: error.message,
                isPossible: false,
                isValid: false
            });
        }
    }

    const countryOptions = country_code.slice(0, 20) // Mostrar apenas os primeiros 20 para teste

    return (
        <div className="container mx-auto py-10 px-4">
            <Card className="max-w-2xl mx-auto">
                <CardHeader>
                    <CardTitle>Teste de Validação de Telefone</CardTitle>
                    <CardDescription>
                        Teste a validação de números de telefone com libphonenumber-js
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                    <div className="space-y-4">
                        <div>
                            <Label htmlFor="countryCode">Código do País</Label>
                            <Select value={countryCode} onValueChange={setCountryCode}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Selecione o código do país" />
                                </SelectTrigger>
                                <SelectContent>
                                    {countryOptions.map((country) => (
                                        <SelectItem key={country.code} value={country.code}>
                                            {country.code} ({country.country})
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                        
                        <div>
                            <Label htmlFor="phoneNumber">Número de Telefone</Label>
                            <Input
                                id="phoneNumber"
                                value={phoneNumber}
                                onChange={(e) => setPhoneNumber(e.target.value)}
                                placeholder="Ex: 912345678"
                                type="tel"
                            />
                        </div>

                        <Button onClick={handleTest} disabled={!phoneNumber.trim()}>
                            Testar Validação
                        </Button>
                    </div>

                    {results && (
                        <div className="space-y-4">
                            <h3 className="text-lg font-semibold">Resultados:</h3>
                            
                            <div className="bg-gray-50 p-4 rounded-lg">
                                <div className="space-y-2">
                                    <p><strong>Código ISO:</strong> {results.isoCode || "N/A"}</p>
                                    <p><strong>É Possível:</strong> {results.isPossible ? "✅ Sim" : "❌ Não"}</p>
                                    <p><strong>É Válido:</strong> {results.isValid ? "✅ Sim" : "❌ Não"}</p>
                                    
                                    {results.error && (
                                        <p><strong>Erro:</strong> <span className="text-red-600">{results.error}</span></p>
                                    )}
                                </div>
                            </div>

                            {results.parsedNumber && (
                                <div className="bg-blue-50 p-4 rounded-lg">
                                    <h4 className="font-semibold mb-2">Detalhes do Número Analisado:</h4>
                                    <pre className="text-sm overflow-auto">
                                        {JSON.stringify(results.parsedNumber, null, 2)}
                                    </pre>
                                </div>
                            )}
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    )
}
