import {registerUser, validateRegistration} from "@/utils/requests/register";
import {useMutation, useQuery} from "@tanstack/react-query";

export const useRegisterUser = () => {
    return useMutation({
        mutationFn: registerUser,
        onSuccess: (data) => {
            console.log('Registration successful:', data)
        },
        onError: (error) => {
            console.error('Registration failed:', error)
        }
    });
}

export const useValidateRegistration = (identifier: string, foreign?: boolean) => {
    const query = useQuery({
        queryKey: ['validateRegistration', identifier, foreign],
        queryFn: () => validateRegistration(identifier, foreign),
        enabled: !!identifier,
    });

    return {
        ...query,
        isUserRegistered: query.data ? query.data.success === false : false,
        validationMessage: query.data?.message,
        email: query.data?.data?.email,
        isConflictError: query.isError && (query.error as any)?.response?.status === 409
    };
}